[[headers]]
  for = "/*"
  [headers.values]
    # Protect against XSS attacks
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://maps-api-ssl.google.com https://www.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https://maps-api-ssl.google.com https://www.oferteo.pl https://www.google.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self'; frame-src https://maps-api-ssl.google.com https://www.google.com; object-src 'none'; base-uri 'self'; form-action 'self';"

    # Prevent MIME type sniffing
    X-Content-Type-Options = "nosniff"

    # Protect against clickjacking
    X-Frame-Options = "SAMEORIGIN"

    # Control how much referrer information should be included with requests
    Referrer-Policy = "strict-origin-when-cross-origin"

    # Enable browser features
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self), interest-cohort=()"

    # Enable strict SSL
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"

    # Prevent XSS attacks
    X-XSS-Protection = "1; mode=block"
