{"homepage": "https://geowymiar.com/", "name": "geo-wymiar", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@types/jest": "^26.0.24", "@types/node": "^12.20.52", "@types/react": "^17.0.45", "@types/react-dom": "^17.0.17", "@types/react-helmet": "^6.1.11", "@types/react-router-dom": "^5.3.3", "react": "^18.1.0", "react-dom": "^18.1.0", "react-helmet": "^6.1.0", "react-router-dom": "^5.3.1", "react-scripts": "5.0.1", "sass": "^1.51.0", "typescript": "^4.6.4", "web-vitals": "^1.1.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && copy .htaccess build\\.htaccess", "build:linux": "react-scripts build && cp .htaccess build/.htaccess", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}