<!DOCTYPE html>
<html lang="pl-PL">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#ffffff" />
    <meta
      name="description"
      content="Geo-Wymiar - profesjonalne usługi geodezyjne w Wielkopolsce. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej."
    />
    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://maps-api-ssl.google.com https://www.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https://maps-api-ssl.google.com https://www.oferteo.pl https://www.google.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self'; frame-src https://maps-api-ssl.google.com https://www.google.com; object-src 'none'; base-uri 'self'; form-action 'self';">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <link rel="apple-touch-icon" sizes="57x57" href="%PUBLIC_URL%/favicon/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="%PUBLIC_URL%/favicon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="%PUBLIC_URL%/favicon/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="%PUBLIC_URL%/favicon/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="%PUBLIC_URL%/favicon/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="%PUBLIC_URL%/favicon/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="%PUBLIC_URL%/favicon/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="%PUBLIC_URL%/favicon/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/favicon/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="%PUBLIC_URL%/favicon/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="%PUBLIC_URL%/favicon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="%PUBLIC_URL%/favicon/favicon-16x16.png">
    <link rel="icon" href="%PUBLIC_URL%/favicon/favicon.ico" />
    <link rel="manifest" href="%PUBLIC_URL%/favicon/manifest.json">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="%PUBLIC_URL%/favicon/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">

    <!-- Resource Hints -->
    <link rel="preconnect" href="https://maps-api-ssl.google.com" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://geowymiar.com/">
    <meta property="og:title" content="Usługi geodezyjne Wielkopolska | Geo-Wymiar">
    <meta property="og:description" content="Profesjonalne usługi geodezyjne w Wielkopolsce. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej.">
    <meta property="og:image" content="https://geowymiar.com/logo-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://geowymiar.com/">
    <meta property="twitter:title" content="Usługi geodezyjne Wielkopolska | Geo-Wymiar">
    <meta property="twitter:description" content="Profesjonalne usługi geodezyjne w Wielkopolsce. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej.">
    <meta property="twitter:image" content="https://geowymiar.com/logo-image.jpg">

    <title>Usługi geodezyjne Wielkopolska | Geo-Wymiar</title>

    <script src="%PUBLIC_URL%/app-config.js"></script>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Geo-Wymiar",
      "description": "Usługi Geodezyjno-Kartograficzne inż. Paweł Pieczyński",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Rowerowa 4",
        "addressLocality": "Psary Polskie",
        "postalCode": "62-300",
        "addressCountry": "PL"
      },
      "telephone": "+***********",
      "email": "<EMAIL>",
      "url": "https://geowymiar.com"
    }
    </script>
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>