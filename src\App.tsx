import { BrowserRouter, Switch, Route } from 'react-router-dom';
import './scss/main.scss';

import { HomePage } from './pages/Home';
import { ONas } from './pages/ONas';
import { MisjaIWartosci } from './pages/MisjaIWartosci';
import { ObszarDzialania } from './pages/ObszarDzialania';
import { NaszeUslugi } from './pages/NaszeUslugi';
import { NotFound } from './pages/404';
import { Kontakt } from './pages/Kontakt';
import { ObszarDzialaniaSzczegoly } from './pages/ObszarDzialaniaSzczegoly';
import { ScrollToTop } from './components/scrollToTop';

const App = () => (
  <BrowserRouter>
    {/* ScrollToTop component will handle scrolling to top on route changes */}
    <ScrollToTop />
    <Switch>
      <Route path="/" exact component={HomePage} />
      <Route path="/nasze-uslugi" exact component={NaszeUslugi} />
      <Route path="/o-nas" exact component={ONas} />
      <Route path="/misja-i-wartosci" exact component={MisjaIWartosci} />
      <Route path="/obszar-dzialania" exact component={ObszarDzialania} />
      <Route path="/obszar-dzialania/:cityName" exact component={ObszarDzialaniaSzczegoly} />
      <Route path="/kontakt" exact component={Kontakt} />

      <Route path="/404" exact component={NotFound} />
      <Route path="*" component={NotFound} />
    </Switch>
  </BrowserRouter>
);

export default App;