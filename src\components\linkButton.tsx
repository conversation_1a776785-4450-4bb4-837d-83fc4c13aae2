import { Link } from "react-router-dom";
import arrowIcon from '../images/icon-arrow-right.png';
import arrowIconWhite from '../images/icon-arrow-right-white.png';

export interface ILinkButtonProps {
    linkTo: string;
    buttonText: string;
    customStyle?: any;
    customButtonStyle?: any;
    customClassName?: string;
    target?: string;
    whiteIcon?: boolean;
    // onShowChange: (value: boolean) => void;
}

export const LinkButton = (props: ILinkButtonProps) => {
    const { linkTo, buttonText } = props;
    const isExternalLink = !!props.target && props.target === '_blank';

    const buttonContent = (
        <button
            className="custom-button"
            style={props.customButtonStyle}
            aria-label={buttonText}
        >
            {buttonText}
            <img
                src={props.whiteIcon ? arrowIconWhite : arrowIcon}
                className="custom-button__icon"
                alt=""
                aria-hidden="true"
                loading="lazy"
            />
        </button>
    );

    return isExternalLink ? (
        <a
            href={linkTo}
            target={props.target}
            rel="noopener noreferrer"
            className={`custom-button__link-wrapper ${props.customClassName || ''}`}
            style={props.customStyle}
            aria-label={`${buttonText} (otwiera się w nowej karcie)`}
        >
            {buttonContent}
        </a>
    ) : (
        <Link
            to={linkTo}
            target={props.target}
            className={`custom-button__link-wrapper ${props.customClassName || ''}`}
            style={props.customStyle}
            aria-label={buttonText}
        >
            {buttonContent}
        </Link>
    );
}