import { Link } from "react-router-dom";
import logo from '../images/logo-small.png';
// import facebookLogo from '../images/facebook-logo.png';
import phoneIcon from '../images/phone-icon.png';
import menuIcon from '../images/icon-menu.png';
import closeIcon from '../images/icon-close-green.png';
import { useState } from "react";
import { isMobile } from "../utils/utils";

export interface INavbarProps {
    currentPage: string;
    // onShowChange: (value: boolean) => void;
}

export const Navbar = (props: INavbarProps) => {
    const { currentPage } = props;

    const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);

    return <nav className={`nav ${showMobileMenu && 'nav--open'}`} role="navigation" aria-label="Menu główne">
        <div className={`nav__wrapper`}>
            <Link to="/" aria-label="Strona główna">
                <img src={logo} title="Strona główna" className="nav__logo" alt="Logo firmy Geo-Wymiar" />
            </Link>

            <div className="nav__menu-wrapper" role="menubar" aria-label="Menu nawigacyjne">
                <Link to="/o-nas" className={`nav__menu-item ${currentPage === 'o-nas' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'o-nas' ? 'page' : undefined}>O nas</Link>
                <Link to="/misja-i-wartosci" className={`nav__menu-item ${currentPage === 'misja-i-wartosci' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'misja-i-wartosci' ? 'page' : undefined}>Misja i wartości</Link>
                <Link to="/nasze-uslugi" className={`nav__menu-item ${currentPage === 'nasze-uslugi' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'nasze-uslugi' ? 'page' : undefined}>Nasze usługi</Link>
                <Link to="/obszar-dzialania" className={`nav__menu-item ${(currentPage === 'obszar-dzialania' || currentPage === 'obszar-dzialania-szczegoly') && 'nav__menu-item--active'}`} role="menuitem" aria-current={(currentPage === 'obszar-dzialania' || currentPage === 'obszar-dzialania-szczegoly') ? 'page' : undefined}>Obszar działania</Link>
                <Link to="/kontakt" className={`nav__menu-item ${currentPage === 'kontakt' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'kontakt' ? 'page' : undefined}>Kontakt</Link>

                {/* <a href="https://facebook.com" target="_blank" rel="noreferrer">
                    <img src={facebookLogo} title="Facebook" className="nav__social-media-logo" alt="Facebook logo" />
                </a> */}

                { isMobile() ?
                    <a href="tel:+48888847055" aria-label="Zadzwoń do nas: 888 847 055">
                        <img src={phoneIcon} title="Kontakt" className="nav__social-media-logo" alt="Ikona telefonu" loading="lazy" />
                    </a>
                    :
                    <Link to="/kontakt" aria-label="Przejdź do strony kontaktowej">
                        <img src={phoneIcon} title="Kontakt" className="nav__social-media-logo" alt="Ikona telefonu" loading="lazy" />
                    </Link>
                }
            </div>

            <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="nav__menu-button"
                aria-label="Otwórz menu mobilne"
                aria-expanded={showMobileMenu}
                aria-controls="mobile-menu"
            >
                <img className="nav__menu-icon" src={menuIcon} alt="" />
            </button>

            <div id="mobile-menu" className={`nav__menu-wrapper-mobile ${showMobileMenu && 'nav__menu-wrapper-mobile--show'}`} role="menu" aria-label="Menu mobilne" aria-hidden={!showMobileMenu}>
                <div className={`nav--mobile ${showMobileMenu && 'nav--open'}`}>
                    <Link to="/" aria-label="Strona główna">
                        <img src={logo} title="Strona główna" className="nav__logo" alt="Logo firmy Geo-Wymiar" />
                    </Link>

                    <button
                        onClick={() => setShowMobileMenu(!showMobileMenu)}
                        className="nav__close-button"
                        aria-label="Zamknij menu mobilne"
                    >
                        <img className="nav__close-icon" src={closeIcon} alt="" />
                    </button>
                </div>

                <Link to="/o-nas" className={`nav__menu-item ${currentPage === 'o-nas' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'o-nas' ? 'page' : undefined}>O nas</Link>
                <Link to="/misja-i-wartosci" className={`nav__menu-item ${currentPage === 'misja-i-wartosci' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'misja-i-wartosci' ? 'page' : undefined}>Misja i wartości</Link>
                <Link to="/nasze-uslugi" className={`nav__menu-item ${currentPage === 'nasze-uslugi' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'nasze-uslugi' ? 'page' : undefined}>Nasze usługi</Link>
                <Link to="/obszar-dzialania" className={`nav__menu-item ${currentPage === 'obszar-dzialania' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'obszar-dzialania' ? 'page' : undefined}>Obszar działania</Link>
                <Link to="/kontakt" className={`nav__menu-item ${currentPage === 'kontakt' && 'nav__menu-item--active'}`} role="menuitem" aria-current={currentPage === 'kontakt' ? 'page' : undefined}>Kontakt</Link>

                {/* <a href="https://facebook.com" target="_blank" rel="noreferrer">
                    <img src={facebookLogo} title="Facebook" className="nav__social-media-logo" alt="Facebook logo" />
                </a> */}

                { isMobile() ?
                    <a href="tel:+48888847055" aria-label="Zadzwoń do nas: 888 847 055" role="menuitem">
                        <img src={phoneIcon} title="Kontakt" className="nav__social-media-logo" alt="Ikona telefonu" loading="lazy" />
                    </a>
                    :
                    <Link to="/kontakt" aria-label="Przejdź do strony kontaktowej" role="menuitem">
                        <img src={phoneIcon} title="Kontakt" className="nav__social-media-logo" alt="Ikona telefonu" loading="lazy" />
                    </Link>
                }
            </div>
        </div>
    </nav>;
}