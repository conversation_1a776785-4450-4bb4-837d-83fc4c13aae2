import { Footer } from "../../components/footer";
import { Navbar } from "../../components/navbar";
import { Banner } from "../../components/banner";
import { LinkButton } from "../../components/linkButton";
import { useEffect } from "react";
import { Helmet } from "react-helmet";
import AppConfig from "../../utils/appconfig";

export function NotFound() {
  useEffect(() => {
    document.title = AppConfig.pageTitles.stronaNieZnaleziona;
  }, []);

  return (
    <div className="App theme-light">
      {/* Add SEO meta tags to prevent indexing */}
      <Helmet>
        <title>{AppConfig.pageTitles.stronaNieZnaleziona}</title>
        <meta name="robots" content="noindex, nofollow" />
        <meta name="googlebot" content="noindex, nofollow" />
        <meta name="description" content="Strona nie została znaleziona. Wró<PERSON> do strony głównej Geo-Wymiar." />
        <link rel="canonical" href="https://geowymiar.com" />

        {/* Open Graph tags for better social sharing */}
        <meta property="og:title" content={AppConfig.pageTitles.stronaNieZnaleziona} />
        <meta property="og:description" content="Strona nie została znaleziona. Wróć do strony głównej Geo-Wymiar." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://geowymiar.com/404" />

        {/* Structured data for search engines */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Strona główna",
                "item": "https://geowymiar.com"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Strona nie znaleziona",
                "item": "https://geowymiar.com/404"
              }
            ]
          })}
        </script>
      </Helmet>

      <Navbar currentPage={'home'} />

      <Banner
        title={'Strona nie została znaleziona'}
        titlePrimary={'Błąd 404'}
        paragraph1Text={'Przepraszamy, ale strona której szukasz nie istnieje lub została przeniesiona.'}
      />

      <div className="content-wrapper">
        <div className="not-found__container">
          <h2 className="not-found__title">
            Co możesz zrobić?
          </h2>

          <ul className="not-found__list">
            <li className="not-found__list-item">
              <p>Sprawdź, czy adres URL został wpisany poprawnie</p>
            </li>
            <li className="not-found__list-item">
              <p>Wróć do <a href="/" className="not-found__link">strony głównej</a></p>
            </li>
            <li className="not-found__list-item">
              <p>Skontaktuj się z nami, jeśli potrzebujesz pomocy</p>
            </li>
          </ul>

          <div className="not-found__popular-section">
            <h3 className="not-found__popular-title">
              Popularne strony
            </h3>

            <div className="not-found__buttons-container">
              <LinkButton linkTo="/" buttonText="Strona główna" />
              <LinkButton linkTo="/nasze-uslugi" buttonText="Nasze usługi" />
              <LinkButton linkTo="/obszar-dzialania" buttonText="Obszar działania" />
              <LinkButton linkTo="/kontakt" buttonText="Kontakt" />
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
