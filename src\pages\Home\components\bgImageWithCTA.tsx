import { LinkButton } from "../../../components/linkButton";

export interface IBgImageWithCTAProps {
    title: string;
    text: any;
    linkTo: string;
    buttonText: string;
    picture: any;
    // onShowChange: (value: boolean) => void;
}

export const BgImageWithCTA = (props: IBgImageWithCTAProps) => {
    const { title, text, picture, linkTo, buttonText } = props;

    return <section className="bg-image__wrapper" aria-labelledby="main-heading">
        <div className="bg-image__picture-wrapper" style={{backgroundImage: `url(${picture})`}} role="img" aria-label="Tło strony głównej - usługi geodezyjne"></div>

        <div className="bg-image__text-wrapper">
            <h1 id="main-heading" className="bg-image__title">{title}</h1>
            <p className="bg-image__caption">{text}</p>

            <LinkButton whiteIcon={true} buttonText={buttonText} linkTo={linkTo} customClassName="bg-image__button-link-wrapper" />
        </div>
    </section>;
}