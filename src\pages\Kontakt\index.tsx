import { Navbar } from '../../components/navbar';

import { Footer } from '../../components/footer';
import { useEffect } from 'react';
import AppConfig from '../../utils/appconfig';
import { SectionTitle } from '../../components/sectionTitle';
import { OferteoEmbedded } from './components/oferteoEmbedded';
import { Helmet } from 'react-helmet';

const currentPage: string = 'kontakt';

export function Kontakt() {
  useEffect(() => {
    document.title = AppConfig.pageTitles.kontakt;
  }, []);

  return (
    <div className="App theme-light">
      <Helmet>
        <title>{AppConfig.pageTitles.kontakt}</title>
        <meta name="description" content="Skontaktuj się z firmą Geo-Wymiar. Profesjonalne usługi geodezyjne we Wrześni i okolicach. Telefon: 888 847 055, email: <EMAIL>" />
        <link rel="canonical" href="https://geowymiar.com/kontakt" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://geowymiar.com/kontakt" />
        <meta property="og:title" content={AppConfig.pageTitles.kontakt} />
        <meta property="og:description" content="Skontaktuj się z firmą Geo-Wymiar. Profesjonalne usługi geodezyjne we Wrześni i okolicach. Telefon: 888 847 055, email: <EMAIL>" />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://geowymiar.com/kontakt" />
        <meta property="twitter:title" content={AppConfig.pageTitles.kontakt} />
        <meta property="twitter:description" content="Skontaktuj się z firmą Geo-Wymiar. Profesjonalne usługi geodezyjne we Wrześni i okolicach. Telefon: 888 847 055, email: <EMAIL>" />

        {/* Structured data for search engines */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Strona główna",
                "item": "https://geowymiar.com"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Kontakt",
                "item": "https://geowymiar.com/kontakt"
              }
            ]
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ContactPage",
            "mainEntity": {
              "@type": "ProfessionalService",
              "name": "Geo-Wymiar",
              "image": "https://geowymiar.com/logo-image.jpg",
              "telephone": "+48888847055",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "Rowerowa 4",
                "addressLocality": "Psary Polskie",
                "postalCode": "62-300",
                "addressRegion": "Wielkopolska",
                "addressCountry": "PL"
              },
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": "52.344143",
                "longitude": "17.535938"
              },
              "openingHoursSpecification": {
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "opens": "08:00",
                "closes": "16:00"
              }
            }
          })}
        </script>
      </Helmet>

      <Navbar currentPage={currentPage} />

      {/* <div className="content-wrapper">
        <div className='kontakt__sections-wrapper'> */}
          <div className="kontakt__content-wrapper content-wrapper">
            <SectionTitle title={"Kontakt"} titlePrimary={"Geo-Wymiar"} customClassName="kontakt__section-title" />

            <p className='kontakt__contact-info-text'>Zapraszamy do bezpośredniego kontaktu</p>

            <div className='kontakt__contact-info-group'>
              <h4 className='kontakt__contact-info-subtitle'>
                Tel.: &nbsp;&nbsp;
                <a className='kontakt__contact-info-phone' href="tel:+48888847055">888 847 055</a>
              </h4>
              <h4 className='kontakt__contact-info-subtitle'>
                Mail: &nbsp;&nbsp;
                <a className='kontakt__contact-info-email' href="mailto:<EMAIL>"><EMAIL></a>
              </h4>
            </div>

            <div className='kontakt__contact-info-group'>
              <p className='kontakt__contact-info-subtitle' style={{marginBottom: 15}}>Biuro</p>

              <h4 className='kontakt__contact-info-title'><a href="geo:52.34393191843817,17.535065154346814;">Psary Polskie, ulica Rowerowa 4</a></h4>
              <h4 className='kontakt__contact-info-title'>62-300 Psary Polskie</h4>
              <h4 className='kontakt__contact-info-title'>(wizyta po wcześniejszym kontakcie)</h4>

              <h4 className='kontakt__contact-info-title' style={{marginTop: 50}}>Z przyjemnością odpowiemy na Państwa wiadomości! </h4>
            </div>

            <OferteoEmbedded />

            <div className="kontakt__map-wrapper">
              <iframe
                sandbox="allow-scripts allow-popups allow-forms allow-same-origin allow-popups-to-escape-sandbox allow-downloads allow-modals"
                style={{border: 0}}
                src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d4874.90013118485!2d17.535902!3d52.344122000000006!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4704eb32db35811b%3A0x2d3f1b4cb2bfdc99!2sGEO-WYMIAR!5e0!3m2!1spl!2sus!4v1747781050638!5m2!1spl!2sus"
                allowFullScreen={true}
                className="kontakt__map"
                title="Lokalizacja firmy Geo-Wymiar na mapie Google"
                aria-labelledby="map-title"
                aria-label='MAP, Geo-Wymiar'
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              >
              </iframe>
              
              <div className="kontakt__map-accessibility">
                <a
                  href="https://maps.app.goo.gl/cjmygZo5ay63Kji89"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="kontakt__map-link"
                  aria-label="Otwórz mapę w Google Maps (otwiera się w nowej karcie)"
                >
                  Otwórz mapę w Google Maps
                </a>
              </div>
            </div>
          </div>
        {/* </div> */}
      {/* </div> */}

      <Footer />
    </div>
  );
}
