import { Navbar } from '../../components/navbar';
import sectionImage from '../../images/misja-i-wartosci/section.jpg';
import { Footer } from '../../components/footer';
import { Banner } from '../../components/banner';
import { useEffect } from 'react';
import AppConfig from '../../utils/appconfig';
import { Helmet } from 'react-helmet';

const currentPage: string = 'misja-i-wartosci';

export function MisjaIWartosci() {
  useEffect(() => {
    document.title = AppConfig.pageTitles.misjaIWartosci;
  }, []);

  return (
    <div className="App theme-light">
      <Helmet>
        <title>{AppConfig.pageTitles.misjaIWartosci}</title>
        <meta name="description" content="Misja i wartości firmy Geo-Wymiar. Uczciwość, profesjonalizm, doświadczenie i komunikacja to fundamenty naszej pracy w branży geodezyjnej." />
        <link rel="canonical" href="https://geowymiar.com/misja-i-wartosci" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://geowymiar.com/misja-i-wartosci" />
        <meta property="og:title" content={AppConfig.pageTitles.misjaIWartosci} />
        <meta property="og:description" content="Misja i wartości firmy Geo-Wymiar. Uczciwość, profesjonalizm, doświadczenie i komunikacja to fundamenty naszej pracy w branży geodezyjnej." />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://geowymiar.com/misja-i-wartosci" />
        <meta property="twitter:title" content={AppConfig.pageTitles.misjaIWartosci} />
        <meta property="twitter:description" content="Misja i wartości firmy Geo-Wymiar. Uczciwość, profesjonalizm, doświadczenie i komunikacja to fundamenty naszej pracy w branży geodezyjnej." />

        {/* Structured data for search engines */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Strona główna",
                "item": "https://geowymiar.com"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Misja i wartości",
                "item": "https://geowymiar.com/misja-i-wartosci"
              }
            ]
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "speakable": {
              "@type": "SpeakableSpecification",
              "cssSelector": [".wartosci__section-text"]
            },
            "name": "Misja i wartości - Geodezja z pasją | Geo-Wymiar",
            "description": "Misja i wartości firmy Geo-Wymiar. Uczciwość, profesjonalizm, doświadczenie i komunikacja to fundamenty naszej pracy w branży geodezyjnej.",
            "mainEntity": {
              "@type": "ItemList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Uczciwość",
                  "description": "Każde zlecenie traktujemy tak, jakbyśmy wykonywali je dla siebie. Nie zwlekamy z terminami i przedstawiamy terminy zgodne z rzeczywistością."
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Profesjonalizm",
                  "description": "Posiadamy sprzęt i umiejętności najwyższej klasy, a nasza praca jest wyliczona co do milimetra."
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": "Doświadczenie",
                  "description": "Ponad 15 lat pracy w branży geodezyjnej zobowiązuje. Podejmujemy się nawet najtrudniejszych wyzwań."
                },
                {
                  "@type": "ListItem",
                  "position": 4,
                  "name": "Komunikacja",
                  "description": "Współpracujemy z Klientami na jasnych zasadach i informujemy o postępach prac."
                }
              ]
            }
          })}
        </script>
      </Helmet>

      <Navbar currentPage={currentPage} />

      <Banner title={'Misja i wartości'} titlePrimary={'Geo-Wymiar'}
        paragraph1Text={'Każdego  dnia  dokładamy  cegiełkę  do  spełnienia  Twoich  marzeń  o  wyjątkowych nieruchomościach, realizując zlecenia geodezyjne.'}
      />
      {/* customClass="wartosci__banner" */}

      <div className="wartosci__section-wrapper">
        <img src={sectionImage} className="wartosci__image" alt="Zawodnik na wózku inwalidzkim podczas wyścigu" loading="lazy" />

        <div className="wartosci__section-text-wrapper">
          <div className="wartosci__section-text-row">
            <span className="wartosci__section-text-number">1</span>
            <p className="wartosci__section-text">
              <span className="o-nas__text--bold">Uczciwość</span> - każde zlecenie traktujemy tak, jakbyśmy wykonywali je dla siebie. Nie zwlekamy z terminami i przedstawiamy terminy zgodne z rzeczywistością.
            </p>
          </div>

          <div className="wartosci__section-text-row">
            <span className="wartosci__section-text-number">2</span>
            <p className="wartosci__section-text">
              <span className="o-nas__text--bold">Profesjonalizm</span> - posiadamy sprzęt i umiejętności najwyższej klasy, a nasza praca jest wyliczona co do milimetra.
            </p>
          </div>

          <div className="wartosci__section-text-row">
            <span className="wartosci__section-text-number">3</span>
            <p className="wartosci__section-text">
              <span className="o-nas__text--bold">Doświadczenie</span> - ponad 15 lat pracy w branży geodezyjnej zobowiązuje. Podejmujemy się nawet najtrudniejszych wyzwań.
            </p>
          </div>

          <div className="wartosci__section-text-row">
            <span className="wartosci__section-text-number">4</span>
            <p className="wartosci__section-text">
              <span className="o-nas__text--bold">Komunikacja</span> - współpracujemy z Klientami na jasnych zasadach i informujemy o postępach prac.
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
