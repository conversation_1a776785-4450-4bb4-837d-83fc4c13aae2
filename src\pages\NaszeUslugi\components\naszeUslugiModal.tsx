import { LinkButton } from '../../../components/linkButton';
import { SectionTitle } from '../../../components/sectionTitle';
import iconClose from '../../../images/icon-close-green.png';
import { useEffect, useRef } from 'react';

export interface INaszeUslugiModalProps {
    hide?: boolean;
    image: any;
    logo?: any;
    logoAlt?: string;
    title: string;
    titlePrimary: string;
    constName?: string;
    constDate?: string;
    constShares?: string;
    text: string;
    tileId: number;
    constName2?: string;
    constDate2?: string;
    constShares2?: string;
    text2?: string;
    link?: string;
    linkText?: string;
    onClose: (tileId: number) => void;
}

export const NaszeUslugiModal = (props: INaszeUslugiModalProps) => {
    const modalRef = useRef<HTMLDivElement>(null);

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            props.onClose(props.tileId);
        }
    };

    // Focus the modal when it opens
    useEffect(() => {
        if (!props.hide && modalRef.current) {
            modalRef.current.focus();
        }
    }, [props.hide]);

    return (
        <div
            ref={modalRef}
            className={`nasze-uslugi-modal__blend ${props.hide ? 'nasze-uslugi-modal__blend--hide' : ''}`}
            role="dialog"
            aria-modal="true"
            aria-labelledby="modal-title"
            aria-hidden={props.hide}
            onKeyDown={handleKeyDown}
            tabIndex={0} // Make the div focusable
        >
            <div
                className={`nasze-uslugi-modal ${props.hide ? 'nasze-uslugi-modal--hide' : ''}`}
                role="document"
            >
                <div className={`nasze-uslugi-modal__banner-warpper`} style={{backgroundImage: `url(${props.image})`}}>
                    <button
                        onClick={() => props.onClose(props.tileId)}
                        className="nasze-uslugi-modal__close-button"
                        aria-label="Zamknij okno"
                    >
                        <img className="nasze-uslugi-modal__close-icon" src={iconClose} alt="" />
                    </button>

                    {props.logo &&
                        <img
                            className="nasze-uslugi-modal__banner-logo"
                            src={props.logo}
                            alt={props.logoAlt || ""}
                            title={props.logoAlt}
                            loading="lazy"
                        />
                    }
                </div>

                <div className={`nasze-uslugi-modal__content-warpper content-wrapper`}>
                    <div id="modal-title">
                        <SectionTitle title={props.title} titlePrimary={props.titlePrimary} />
                    </div>

                { (!!props.constName && !!props.constDate && !!props.constShares) &&
                    <div className={`nasze-uslugi-modal__constants-wrapper`}>
                        <div className={`nasze-uslugi-modal__constants-row`}>
                            <p className={`nasze-uslugi-modal__constants-title`}>Nazwa spółki</p>
                            <h6 className={`nasze-uslugi-modal__constants-value`}>{props.constName}</h6>
                        </div>

                        <div className={`nasze-uslugi-modal__constants-row`}>
                            <p className={`nasze-uslugi-modal__constants-title`}>Data inwestycji</p>
                            <h6 className={`nasze-uslugi-modal__constants-value`}>{props.constDate}</h6>
                        </div>

                        <div className={`nasze-uslugi-modal__constants-row`}>
                            <p className={`nasze-uslugi-modal__constants-title`}>Udziały</p>
                            <h6 className={`nasze-uslugi-modal__constants-value`}>{props.constShares}</h6>
                        </div>
                    </div>
                }

                <p className={`nasze-uslugi-modal__text content__text`}>{props.text}</p>

                { (!!props.constName2 && !!props.constDate2 && !!props.constShares2) &&
                    <div className={`nasze-uslugi-modal__constants-wrapper`}>
                        <div className={`nasze-uslugi-modal__constants-row`}>
                            <p className={`nasze-uslugi-modal__constants-title`}>Nazwa spółki</p>
                            <h6 className={`nasze-uslugi-modal__constants-value`}>{props.constName2}</h6>
                        </div>

                        <div className={`nasze-uslugi-modal__constants-row`}>
                            <p className={`nasze-uslugi-modal__constants-title`}>Data inwestycji</p>
                            <h6 className={`nasze-uslugi-modal__constants-value`}>{props.constDate2}</h6>
                        </div>

                        <div className={`nasze-uslugi-modal__constants-row`}>
                            <p className={`nasze-uslugi-modal__constants-title`}>Udziały</p>
                            <h6 className={`nasze-uslugi-modal__constants-value`}>{props.constShares2}</h6>
                        </div>
                    </div>
                }

                { !!props.text2 &&
                    <p className={`nasze-uslugi-modal__text content__text`}>{props.text2}</p>
                }

                { !!props.link &&
                    <LinkButton buttonText={!!props.linkText ? props.linkText : props.link} linkTo={props.link} target={"_blank"}
                        customStyle={{width: '100%'}}
                        customButtonStyle={{width: '100%', padding: '20px 18px'}} />
                }
            </div>
        </div>
    </div>
    );
}