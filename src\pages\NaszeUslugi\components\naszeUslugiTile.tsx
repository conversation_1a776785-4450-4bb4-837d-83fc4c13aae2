import iconArrow from '../../../images/icon-arrow-right.png';

export interface INaszeUslugiTileProps {
    image: any;
    imageAlt: string;
    title: string;
    tileId: number;
    invisible?: boolean;
    onClick: (tileId: number) => void;
}

export const NaszeUslugiTile = (props: INaszeUslugiTileProps) => {
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            props.onClick(props.tileId);
        }
    };

    return (
        <div
            className={`nasze-uslugi-tile ${props.invisible ? 'nasze-uslugi-tile--invisible' : ''}`}
            onClick={() => props.onClick(props.tileId)}
            onKeyDown={handleKeyDown}
            tabIndex={0}
            role="button"
            aria-label={`<PERSON><PERSON>ż szczegóły usługi: ${props.title}`}
            aria-pressed="false"
        >
            <div className="nasze-uslugi-tile__image-wrapper">
                <img
                    src={props.image}
                    className="nasze-uslugi-tile__image"
                    alt={props.imageAlt}
                    loading="lazy"
                />
            </div>

            <div className="nasze-uslugi-tile__title-wrapper">
                <p className="nasze-uslugi-tile__title">{props.title}</p>

                <img
                    src={iconArrow}
                    className="nasze-uslugi-tile__arrow-icon"
                    alt=""
                    aria-hidden="true"
                    loading="lazy"
                />
            </div>
        </div>
    );
}