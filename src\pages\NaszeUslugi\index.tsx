import { Navbar } from '../../components/navbar';

import tile1 from '../../images/nasze-uslugi/tiles/1.jpg';
import tile2 from '../../images/nasze-uslugi/tiles/2.jpg';
import tile3 from '../../images/nasze-uslugi/tiles/3.jpg';
import tile4 from '../../images/nasze-uslugi/tiles/4.jpg';
import tile5 from '../../images/nasze-uslugi/tiles/5.jpg';
import tile6 from '../../images/nasze-uslugi/tiles/6.jpg';
import tile7 from '../../images/nasze-uslugi/tiles/7.jpg';
import tile8 from '../../images/nasze-uslugi/tiles/8.jpg';

import { Footer } from '../../components/footer';
import { NaszeUslugiTile } from './components/naszeUslugiTile';
import { useEffect, useState } from 'react';
import AppConfig from '../../utils/appconfig';
import { NaszeUslugiModal } from './components/naszeUslugiModal';
import { Banner } from '../../components/banner';
import { Helmet } from 'react-helmet';

const currentPage: string = 'nasze-uslugi';

export function NaszeUslugi() {
  const [openModalId, setOpenModalId] = useState<number>(-1);

  useEffect(() => {
    document.title = AppConfig.pageTitles.naszeUslugi;
  }, []);

  const handleTileClick: any = (tileId: number) => {
    setOpenModalId(tileId);
  };

  return (
    <div className="App theme-light">
      <Helmet>
        <title>{AppConfig.pageTitles.naszeUslugi}</title>
        <meta name="description" content="Kompleksowe usługi geodezyjne w Wielkopolsce. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości, inwentaryzacje powykonawcze i więcej." />
        <link rel="canonical" href="https://geowymiar.com/nasze-uslugi" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://geowymiar.com/nasze-uslugi" />
        <meta property="og:title" content={AppConfig.pageTitles.naszeUslugi} />
        <meta property="og:description" content="Kompleksowe usługi geodezyjne w Wielkopolsce. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości, inwentaryzacje powykonawcze i więcej." />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://geowymiar.com/nasze-uslugi" />
        <meta property="twitter:title" content={AppConfig.pageTitles.naszeUslugi} />
        <meta property="twitter:description" content="Kompleksowe usługi geodezyjne w Wielkopolsce. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości, inwentaryzacje powykonawcze i więcej." />

        {/* Structured data for search engines */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Strona główna",
                "item": "https://geowymiar.com"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Nasze usługi",
                "item": "https://geowymiar.com/nasze-uslugi"
              }
            ]
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ItemList",
            "itemListElement": [
              {
                "@type": "Service",
                "position": 1,
                "name": "Mapy do celów projektowych",
                "url": "https://geowymiar.com/nasze-uslugi",
                "provider": {
                  "@type": "LocalBusiness",
                  "name": "Geo-Wymiar"
                },
                "description": "Jeśli rozpoczynasz inwestycję, z pewnością potrzebna będzie Ci mapa do celów projektowych. To dokument niezbędny do realizacji inwestycji, która wymaga pozwolenia na budowę."
              },
              {
                "@type": "Service",
                "position": 2,
                "name": "Ustalenie przebiegu granic",
                "url": "https://geowymiar.com/nasze-uslugi",
                "provider": {
                  "@type": "LocalBusiness",
                  "name": "Geo-Wymiar"
                },
                "description": "To szereg czynności, które są prowadzone, by określić przebieg granic działek ewidencyjnych lub nieruchomości."
              },
              {
                "@type": "Service",
                "position": 3,
                "name": "Wznowienia znaków granicznych",
                "url": "https://geowymiar.com/nasze-uslugi",
                "provider": {
                  "@type": "LocalBusiness",
                  "name": "Geo-Wymiar"
                },
                "description": "Wznowienie znaków granicznych to proces, który polega na odtworzeniu zniszczonych lub uszkodzonych znaków granicznych na podstawie analizy pierwotnych dokumentów."
              }
            ]
          })}
        </script>
      </Helmet>

      { openModalId === 0 &&
        <NaszeUslugiModal tileId={0} title='Mapy do celów projektowych' titlePrimary='Nasze usługi' image={tile1}
          text={`Jeśli rozpoczynasz inwestycję, z pewnością potrzebna będzie Ci mapa do celów projektowych. To dokument niezbędny do realizacji inwestycji, która wymaga pozwolenia na budowę. Będzie potrzebny w przypadku budowy domu, sieci lub innego obiektu budowlanego. Mapa do celów projektowych jest fundamentem, na którym projektanci wykonują projekt budowlany.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      { openModalId === 1 &&
        <NaszeUslugiModal tileId={0} title='Ustalenie przebiegu granic' titlePrimary='Nasze usługi' image={tile2}
          text={`To szereg czynności, które są prowadzone, by określić przebieg granic działek ewidencyjnych lub nieruchomości. Przebieg tychże granic zostaje odzwierciedlony w gruncie, za pośrednictwem znaków granicznych.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      { openModalId === 2 &&
        <NaszeUslugiModal tileId={0} title='Badanie stanu prawnego nieruchomości' titlePrimary='Nasze usługi' image={tile3}
          text={`W przypadku nieruchomości i gruntów zdarzają się przypadki niejasności w dokumentacji prawnej oraz w księgach wieczystych. Badanie stanu prawnego nieruchomości to czynności polegające na zweryfikowaniu stanu prawnego, który jest zawarty w aktach i księgach wieczystych.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      { openModalId === 3 &&
        <NaszeUslugiModal tileId={0} title='Dokumentacja do celów prawnych' titlePrimary='Nasze usługi' image={tile4}
          text={`Dokumentację geodezyjną i kartograficzną do celów prawnych stanowią mapy, rejestry, protokoły, wykazy, szkice, wyrysy, odpisy, wypisy i inne dokumenty, które ze względu na ich treść i urzędowe klauzule umożliwiają dokonywanie czynności w postępowaniu sądowym, administracyjnym lub przed notariuszem np. w celu dokonania wpisu w księdze wieczystej lub założenia nowej księgi wieczystej.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      { openModalId === 4 &&
        <NaszeUslugiModal tileId={0} title='Inwentaryzacje powykonawcze obiektów budowlanych' titlePrimary='Nasze usługi' image={tile5}
          text={`Zakończenie ostatniego etapu realizacji budowy domu, sieci czy innych obiektów wiąże się ze spełnieniem podstawowych wymogów formalnych. Niezbędna jest wówczas inwentaryzacja powykonawcza. To po prostu naniesienie pomierzonych obiektów na mapę. Realizujemy wszelkiego rodzaju inwentaryzacje powykonawcze budynków mieszkalnych,przemysłowych, handlowo-usługowych itp.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      { openModalId === 5 &&
        <NaszeUslugiModal tileId={0} title='Podziały nieruchomości' titlePrimary='Nasze usługi' image={tile6}
          text={`Podziały nieruchomości to jedno z najczęstszych opracowań do celów prawnych w geodezji. Proces ten jest niezbędny w przypadku konieczności wydzielenia z nieruchomości nowych działek np. pod budowę domu czy też w celu zniesienia współwłasności. Z uwagi na różnego rodzaju warunki jakie muszą być spełnione w celu wydzielenia nowych działek dokładamy wszelkich starań aby przedstawić najlepsze rozwiązania.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      { openModalId === 6 &&
        <NaszeUslugiModal tileId={0} title='Wytyczenie budynków i sieci uzbrojenia terenu' titlePrimary='Nasze usługi' image={tile7}
          text={`Wytyczenie budynków i sieci uzbrojenia terenu wiąże się z pracą w terenie. Jest to wyznaczenie projektowanych obiektów na podstawie danych z projektu budowlanego. Są to na przykład osie konstrukcyjne budynku lub punkty załamań sieci. Geodeta uprawniony jest w stanie potwierdzić wytyczenie obiektu budowlanego, za pośrednictwem wpisu do dziennika budowy.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      { openModalId === 7 &&
        <NaszeUslugiModal tileId={0} title='Wznowienia znaków granicznych' titlePrimary='Nasze usługi' image={tile8}
          text={`Wznowienie znaków granicznych to proces, który polega na odtworzeniu zniszczonych lub uszkodzonych znaków granicznych na podstawie analizy pierwotnych dokumentów. To praca niezbędna m.in. aby określić właściwą, zgodną ze stanem faktycznym powierzchnię Twojej działki.`}
          onClose={(tileId: number) => setOpenModalId(-1) } />
      }

      <Navbar currentPage={currentPage} />

      {/* <Banner title={'Geo-Wymiar'} titlePrimary={'Usługi Geodezyjno-Kartograficzne inż. Paweł Pieczyński'}
        paragraph1Title={'850'} paragraph1Text={'LOREM IPSUM'} paragraph2Title={'35.000'} paragraph2Text={'DOLOR SIT AMET'} /> */}
      <Banner title={'Nasze usługi'} titlePrimary={'Geo-Wymiar'} />

      <div className="content-wrapper" style={{marginTop: 30}}>
        {/* <SectionTitle title={"Nasze usługi"} titlePrimary={"Geo-Wymiar"} /> */}

        {/* <div className="content__text-wrapper">
          <p className="content__text">Curabitur nulla neque, interdum sed lobortis id, condimentum ut arcu.</p>
          <p className="content__text">Suspendisse eu luctus libero. Proin vel ex lacus. Vivamus mattis faucibus risus, eu semper leo sollicitudin at.</p>
        </div> */}

        <div className="nasze-uslugi__tiles-wrapper">
          <NaszeUslugiTile tileId={0} image={tile1} title="Mapy do celów projektowych" imageAlt="Mapy do celów projektowych" onClick={(tileId: number) => handleTileClick(tileId)} />
          <NaszeUslugiTile tileId={1} image={tile2} title="Ustalenie przebiegu granic" imageAlt="Ustalenie przebiegu granic" onClick={(tileId: number) => handleTileClick(tileId)} />
          <NaszeUslugiTile tileId={2} image={tile3} title="Badanie stanu prawnego nieruchomości" imageAlt="Badanie stanu prawnego nieruchomości" onClick={(tileId: number) => handleTileClick(tileId)} />
          <NaszeUslugiTile tileId={3} image={tile4} title="Dokumentacja do celów prawnych" imageAlt="Dokumentacja do celów prawnych" onClick={(tileId: number) => handleTileClick(tileId)} />
        </div>

        <div className="nasze-uslugi__tiles-wrapper">
          <NaszeUslugiTile tileId={4} image={tile5} title="Inwentaryzacje powykonawcze obiektów budowlanych" imageAlt="Inwentaryzacje powykonawcze obiektów budowlanych" onClick={(tileId: number) => handleTileClick(tileId)} />
          <NaszeUslugiTile tileId={5} image={tile6} title="Podziały nieruchomości" imageAlt="Podziały nieruchomości" onClick={(tileId: number) => handleTileClick(tileId)} />
          <NaszeUslugiTile tileId={6} image={tile7} title="Wytyczenie budynków i sieci uzbrojenia terenu" imageAlt="Wytyczenie budynków i sieci uzbrojenia terenu" onClick={(tileId: number) => handleTileClick(tileId)} />
          <NaszeUslugiTile tileId={7} image={tile8} title="Wznowienia znaków granicznych" imageAlt="Wznowienia znaków granicznych" onClick={(tileId: number) => handleTileClick(tileId)} />
        </div>

        {/* <div className="nasze-uslugi__tiles-wrapper">
          <NaszeUslugiTile tileId={12} image={tile1} title="" imageAlt="" onClick={(tileId: number) => { return }} invisible={true} />
        </div> */}
      </div>

      <Footer />
    </div>
  );
}
