import { Navbar } from '../../components/navbar';
import { Footer } from '../../components/footer';
import { Banner } from '../../components/banner';
import { useEffect } from 'react';
import { Link, useHistory, useParams } from 'react-router-dom';
import obszarDzialaniaData from '../../utils/obszarDzialaniaData';
import { Helmet } from 'react-helmet';

import arrowIcon from '../../images/icon-arrow-right.png';
import AppConfig from '../../utils/appconfig';
import { NotFound } from '../404';

const currentPage: string = 'obszar-dzialania-szczegoly';

export function ObszarDzialaniaSzczegoly() {
  const { cityName } = useParams() as {
    cityName: string;
  };
  const history = useHistory();

  useEffect(() => {
    if(obszarDzialaniaData.hasOwnProperty(cityName)) {
      document.title = `Geodeta ${obszarDzialaniaData[cityName].title} - Usługi geodezyjne | ${AppConfig.baseTabTitle}`;
    } else {
      history.push('/404');   
    }
  }, [cityName, history]);

  return (
    obszarDzialaniaData.hasOwnProperty(cityName) ? (
      <div className="App theme-light">
        <Helmet>
          <title>{`Geodeta ${obszarDzialaniaData[cityName].title} - Usługi geodezyjne | ${AppConfig.baseTabTitle}`}</title>
          <meta name="description" content={`Profesjonalne usługi geodezyjne w ${obszarDzialaniaData[cityName].title}. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej.`} />
          <link rel="canonical" href={`https://geowymiar.com/obszar-dzialania/${cityName}`} />

          {/* Open Graph / Facebook */}
          <meta property="og:type" content="website" />
          <meta property="og:url" content={`https://geowymiar.com/obszar-dzialania/${cityName}`} />
          <meta property="og:title" content={`Geodeta ${obszarDzialaniaData[cityName].title} - Usługi geodezyjne | ${AppConfig.baseTabTitle}`} />
          <meta property="og:description" content={`Profesjonalne usługi geodezyjne w ${obszarDzialaniaData[cityName].title}. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej.`} />

          {/* Twitter */}
          <meta property="twitter:card" content="summary_large_image" />
          <meta property="twitter:url" content={`https://geowymiar.com/obszar-dzialania/${cityName}`} />
          <meta property="twitter:title" content={`Geodeta ${obszarDzialaniaData[cityName].title} - Usługi geodezyjne | ${AppConfig.baseTabTitle}`} />
          <meta property="twitter:description" content={`Profesjonalne usługi geodezyjne w ${obszarDzialaniaData[cityName].title}. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej.`} />

          {/* Structured data for search engines */}
          <script type="application/ld+json">
            {JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Strona główna",
                  "item": "https://geowymiar.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Obszar działania",
                  "item": "https://geowymiar.com/obszar-dzialania"
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": obszarDzialaniaData[cityName].title,
                  "item": `https://geowymiar.com/obszar-dzialania/${cityName}`
                }
              ]
            })}
          </script>
          <script type="application/ld+json">
            {JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Service",
              "name": `Usługi geodezyjne w ${obszarDzialaniaData[cityName].title}`,
              "provider": {
                "@type": "LocalBusiness",
                "name": "Geo-Wymiar",
                "address": {
                  "@type": "PostalAddress",
                  "addressLocality": "Psary Polskie",
                  "addressRegion": "Wielkopolska",
                  "postalCode": "62-300",
                  "streetAddress": "Rowerowa 4"
                }
              },
              "areaServed": {
                "@type": "City",
                "name": obszarDzialaniaData[cityName].title
              },
              "description": `Profesjonalne usługi geodezyjne w ${obszarDzialaniaData[cityName].title}. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej.`
            })}
          </script>
        </Helmet>

        <Navbar currentPage={currentPage} />

        <Banner title={`Obszar działania - ${obszarDzialaniaData[cityName].title}`} titlePrimary={'Geo-Wymiar'} />

        <div className="content-wrapper">
          <Link to={`/obszar-dzialania`} className={`obszar-dzialania__back-link`}>
            <img src={arrowIcon} className="obszar-dzialania__back-link-icon" alt={'Ikona strzałki w lewo'} loading="lazy" />
            {`Powrót`}
          </Link>

          <h1 className="obszar-dzialania__city-title">Usługi geodezyjne {obszarDzialaniaData[cityName].title}</h1>

          <p style={{marginTop: '20px', fontSize: '1rem', textAlign: 'left'}}>{obszarDzialaniaData[cityName].text}</p>

          <div className="obszar-dzialania__services-section">
            <h2 className="obszar-dzialania__services-title">Nasze usługi geodezyjne w miejscowości {obszarDzialaniaData[cityName].title}</h2>
            <ul className="obszar-dzialania__services-list">
              <li>Mapy do celów projektowych</li>
              <li>Ustalenie przebiegu granic</li>
              <li>Tyczenie budynków</li>
              <li>Dokumentacja do celów prawnych</li>
              <li>Inwentaryzacje powykonawcze</li>
              <li>Podziały nieruchomości</li>
              <li>Mapy do celów opiniodawczych</li>
              <li>Wznowienia znaków granicznych</li>
            </ul>

            <div className="obszar-dzialania__cta">
              <Link to="/kontakt" className="custom-button">
                Skontaktuj się z nami
                <img src={arrowIcon} className="custom-button__icon" alt={'Ikona strzałki w prawo'} loading="lazy" />
              </Link>
            </div>
          </div>
        </div>
        
        <Footer />
      </div>
    ) : (
      <NotFound />
    )
  );
}
