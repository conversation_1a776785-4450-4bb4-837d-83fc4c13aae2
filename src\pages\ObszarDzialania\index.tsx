import { Navbar } from '../../components/navbar';
import { Footer } from '../../components/footer';
import { Banner } from '../../components/banner';
import { useEffect, useState } from 'react';
import obszarDzialaniaData from '../../utils/obszarDzialaniaData';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';

import section1Mobile from '../../images/obszar-dzialania/section1-mobile.jpg';
import section1 from '../../images/obszar-dzialania/section1.jpg';
import arrowIcon from '../../images/icon-arrow-right.png';
import AppConfig from '../../utils/appconfig';

const currentPage: string = 'obszar-dzialania';

export function ObszarDzialania() {
  const [allLocations, setAllLocations] = useState<any[]>([]);

  useEffect(() => {
    document.title = AppConfig.pageTitles.obszarDzialania;

    let tempAllLocations: any[] = [];

    for(const key in obszarDzialaniaData) {
      if(Object.prototype.hasOwnProperty.call(obszarDzialaniaData, key)) {
        tempAllLocations.push({key: key, title: obszarDzialaniaData[key].title});
      }
    }

    setAllLocations(tempAllLocations);
  }, []);

  return (
    <div className="App theme-light">
      <Helmet>
        <title>{AppConfig.pageTitles.obszarDzialania}</title>
        <meta name="description" content="Obszar działania firmy Geo-Wymiar. Świadczymy profesjonalne usługi geodezyjne w całej Wielkopolsce, w tym we Wrześni, Poznaniu, Gnieźnie i okolicach." />
        <link rel="canonical" href="https://geowymiar.com/obszar-dzialania" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://geowymiar.com/obszar-dzialania" />
        <meta property="og:title" content="Obszar działania - Usługi geodezyjne Wielkopolska | Geo-Wymiar" />
        <meta property="og:description" content="Obszar działania firmy Geo-Wymiar. Świadczymy profesjonalne usługi geodezyjne w całej Wielkopolsce, w tym we Wrześni, Poznaniu, Gnieźnie i okolicach." />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://geowymiar.com/obszar-dzialania" />
        <meta property="twitter:title" content="Obszar działania - Usługi geodezyjne Wielkopolska | Geo-Wymiar" />
        <meta property="twitter:description" content="Obszar działania firmy Geo-Wymiar. Świadczymy profesjonalne usługi geodezyjne w całej Wielkopolsce, w tym we Wrześni, Poznaniu, Gnieźnie i okolicach." />

        {/* Structured data for search engines */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Strona główna",
                "item": "https://geowymiar.com"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Obszar działania",
                "item": "https://geowymiar.com/obszar-dzialania"
              }
            ]
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            "name": "Usługi geodezyjne w Wielkopolsce",
            "provider": {
              "@type": "LocalBusiness",
              "name": "Geo-Wymiar",
              "address": {
                "@type": "PostalAddress",
                "addressLocality": "Psary Polskie",
                "addressRegion": "Wielkopolska",
                "postalCode": "62-300",
                "streetAddress": "Rowerowa 4"
              }
            },
            "areaServed": {
              "@type": "State",
              "name": "Wielkopolska"
            },
            "description": "Profesjonalne usługi geodezyjne w całej Wielkopolsce. Mapy do celów projektowych, wznowienia znaków granicznych, podziały nieruchomości i więcej."
          })}
        </script>
      </Helmet>

      <Navbar currentPage={currentPage} />

      <Banner title={'Obszar działania'} titlePrimary={'Geo-Wymiar'} />

      <div className="content-wrapper">
        <h1 className="obszar-dzialania__main-title">Obszar działania - Usługi geodezyjne w Wielkopolsce</h1>

        <p className="obszar-dzialania__intro-text">
          Firma Geo-Wymiar świadczy profesjonalne usługi geodezyjne na terenie całej Wielkopolski.
          Nasza siedziba znajduje się w powiecie wrzesińskim, ale realizujemy zlecenia w wielu
          miejscowościach regionu. Poniżej znajdziesz listę głównych lokalizacji, w których działamy.
          Kliknij na wybraną miejscowość, aby dowiedzieć się więcej o naszych usługach w tym obszarze.
        </p>

        <div className="o-nas__section-wrapper">
          <div className="obszar-dzialania__lists-container">
            <h2 className="obszar-dzialania__locations-title">Nasze lokalizacje</h2>

            <div className="obszar-dzialania__lists-wrapper">
              <ul className={`obszar-dzialania__links-list`}>
                {
                  allLocations.map((location, index) => {
                    if(index < allLocations.length / 2) {
                      return <li className={`obszar-dzialania__links-list-item`} key={`location-${index}`}>
                          <Link to={`/obszar-dzialania/${location.key}`} className={`obszar-dzialania__link`}>{location.title}</Link>
                        </li>
                    } else {
                      return null;
                    }
                  })
                }
              </ul>
              <ul className={`obszar-dzialania__links-list`}>
                {
                  allLocations.map((location, index) => {
                    if(index >= allLocations.length / 2) {
                      return <li className={`obszar-dzialania__links-list-item`} key={`location-${index}`}>
                          <Link to={`/obszar-dzialania/${location.key}`} className={`obszar-dzialania__link`}>{location.title}</Link>
                        </li>
                    } else {
                      return null;
                    }
                  })
                }
              </ul>
            </div>
          </div>

          <img src={section1Mobile} className="o-nas__image--mobile" alt="Geodeta podczas pracy z urządzeniem pomiarowym w terenie" loading="lazy" />
          <img src={section1} className="o-nas__image" alt="Geodeta podczas pracy z urządzeniem pomiarowym w terenie" loading="lazy" />
        </div>

        <div className="obszar-dzialania__services-overview">
          <h2 className="obszar-dzialania__services-overview-title">Nasze usługi geodezyjne w Wielkopolsce</h2>

          <p className="obszar-dzialania__services-overview-text">
            W każdej z wymienionych lokalizacji oferujemy pełen zakres usług geodezyjnych, w tym:
          </p>

          <ul className="obszar-dzialania__services-overview-list">
            <li>Mapy do celów projektowych</li>
            <li>Ustalenie przebiegu granic</li>
            <li>Tyczenie budynków</li>
            <li>Dokumentacja do celów prawnych</li>
            <li>Inwentaryzacje powykonawcze</li>
            <li>Podziały nieruchomości</li>
            <li>Mapy do celów opiniodawczych</li>
            <li>Wznowienia znaków granicznych</li>
          </ul>

          <p className="obszar-dzialania__services-overview-text">
            Niezależnie od lokalizacji, gwarantujemy profesjonalne podejście, terminowość oraz
            konkurencyjne ceny. Zapraszamy do kontaktu w celu uzyskania szczegółowych informacji
            lub bezpłatnej wyceny.
          </p>

          <div className="obszar-dzialania__cta">
            <Link to="/kontakt" className="custom-button">
              Skontaktuj się z nami
              <img src={arrowIcon} className="custom-button__icon" alt={'Ikona strzałki w prawo'} loading="lazy" />
            </Link>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
