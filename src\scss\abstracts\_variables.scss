// --- colors

$--theme-primary: --theme-primary;
$--theme-primary-pale: --theme-primary-pale;
$--theme-primary-light: --theme-primary-light;
$--theme-secondary: --theme-secondary;
$--theme-text: --theme-text;
$--theme-background: --theme-background;
$--theme-banner-background: --theme-banner-background;
$--theme-red: --theme-red;
$--theme-green: --theme-green;
$--theme-white: --theme-white;
$--theme-white-dark: --theme-white-dark;

// Light theme (default)
$theme-map-light: (
    $--theme-primary: #87af69,
    $--theme-primary-pale: #b3bcad,
    $--theme-primary-light: #f2f7ed,
    $--theme-secondary: #bddfa3,
    $--theme-text: #000000,
    $--theme-background: #FFFFFF,
    $--theme-banner-background: #e2e7de,
    $--theme-red: #ff0000,
    $--theme-green: #00ff0d,
    $--theme-white: #ffffff,
    $--theme-white-dark: #f9f9f9,
);

$neutral: #AFBAC5;

// --- typography

$lightWeight: 300;
$regularWeight: 400;
$semiBoldWeight: 600;
$boldWeight: 700;
$superBoldWeight: 800;

// --- breakpoints

$breakpoints: (
    'tablet':  ( min-width:  481px ),
    'large-tablet':  ( min-width:  769px ),
    'small-screen': ( min-width:  992px ),
    'desktop':  ( min-width: 1400px )
) !default;

// --- others

$maxWidth: 1400px;
$transitionDuration: 150ms;