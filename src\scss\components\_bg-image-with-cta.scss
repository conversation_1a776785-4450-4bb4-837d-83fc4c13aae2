.bg-image {
    &__wrapper {
        width: 100%;
        height: auto;
        min-height: 450px;
        position: relative;

        @include breakpoint-min('small-screen') {
            min-height: 550px;
        }

        @include breakpoint-min('desktop') {
            min-height: 650px;
        }
    }

    &__picture-wrapper {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        filter: blur(0.15rem);
        width: 100%;
        height: 100%;
    }

    &__text-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
    }

    &__title {
        color: var($--theme-primary);
        font-weight: $boldWeight;
        font-size: 2.2rem;
        margin-bottom: 20px;
        background-color: transparentize($color: rgb(85, 85, 85), $amount: 0.2);
        padding: 30px 30px;
        border-radius: 1rem;
        word-wrap: normal;
        word-break: keep-all;
        white-space: nowrap;

        @include breakpoint-min('tablet') {
            font-size: 3rem;
        }

        @include breakpoint-min('large-tablet') {
            font-size: 4rem;
            padding: 40px;
        }

        @include breakpoint-min('desktop') {
            font-size: 5rem;
            padding: 50px;
        }
    }

    &__caption {
        color: var($--theme-white);
        margin-bottom: 40px;
        background-color: transparentize($color: rgb(85, 85, 85), $amount: 0.2);
        padding: 20px 20px;
        border-radius: 1rem;
        font-size: 1.1rem;

        @include breakpoint-min('tablet') {
            font-size: 1.3rem;
            padding: 25px;
        }

        @include breakpoint-min('large-tablet') {
            font-size: 1.5rem;
            padding: 25px;
        }

        @include breakpoint-min('desktop') {
            font-size: 2rem;
            padding: 30px;
        }
    }

    &__button {
        &-link-wrapper {
            .custom-button {
                background: var($--theme-primary);
                color: var($--theme-white);
                border-radius: 1rem;
                border: 2px solid transparentize($color: rgb(85, 85, 85), $amount: 0.2);
                padding: 8px 15px;
                font-size: 1rem; // 16px
                line-height: 1.75rem; // 28px

                @include breakpoint-min('desktop') {
                    font-size: 1.2rem; // 20px
                    line-height: 2rem; // 32px
                    padding: 12px 25px;
                }

                &:hover {
                    filter: brightness(1.1);
                    transform: scale(1.1);
                }
            }
        }
    }
}