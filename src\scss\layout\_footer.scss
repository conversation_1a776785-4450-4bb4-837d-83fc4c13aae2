.footer {
    background: var($--theme-background);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: center;
    width: 100%;
    margin-top: auto;
    padding-top: 25px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);

    &__wrapper {
        width: 100%;
        max-width: 1920px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 16px;
        box-sizing: border-box;
        margin: 0 auto;
    
        @include breakpoint-min('small-screen') {
            padding: 16px 60px;
            justify-content: flex-start;
        }
    }

    &__text {
        text-transform: uppercase;
        font-size: 0.85rem;
        line-height: 1.2rem;

        @include breakpoint-min('small-screen') {
            margin-right: 60px;
        }

        &--bold {
            font-weight: $boldWeight;
        }
    }

    &__link {
        color: var($--theme-primary);
        display: block;
        text-transform: uppercase;
        font-size: 0.85rem;
        line-height: 1.2rem;

        &--inline {
            display: inline-block;
        }
    }

    &__tile {
        &-wrapper {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-evenly;
            align-items: center;
        }

        width: auto;
        max-width: 50%;
        max-height: 130px;
        margin: 0 25px 25px;

        @include breakpoint-min('large-tablet') {
            max-width: 30%;
        }

        @include breakpoint-min('small-screen') {
            max-width: 22%;
            max-height: 120px;
        }

        @include breakpoint-min('desktop') {
            max-width: 12%;
            max-height: 100px;
        }
    }
}