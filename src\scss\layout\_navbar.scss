.nav {
    background: var(--theme-background);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    width: 100%;

    &--open {
        box-shadow: none !important;
    }

    &--mobile {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 9px 16px;
        box-sizing: border-box;

        @include breakpoint-min('tablet') {
            padding: 16px 45px;
        }

        @include breakpoint-min('large-tablet') {
            padding: 16px 60px;
        }

        @include rwdCustom(1300) {
            padding: 16px 15px 16px 60px;
        }
    }

    &__wrapper {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        max-width: 1920px;
        padding: 9px 16px;
        box-sizing: border-box;

        @include breakpoint-min('tablet') {
            padding: 16px 45px;
        }

        @include breakpoint-min('large-tablet') {
            padding: 16px 60px;
        }

        @include rwdCustom(1300) {
            padding: 16px 15px 16px 60px;
        }
    }

    &__logo {
        transition: $transitionDuration;
        max-width: 500px;
        max-height: 283px;
        width: 85px;
        height: auto;

        @include breakpoint-min('large-tablet') {
            width: 105px;
        }

        &:hover {
            transform: scale(1.05);
        }
    }

    &__social-media-logo {
        transition: $transitionDuration;
        max-width: 45px;
        max-height: 45px;
        width: auto;
        height: auto;

        &:last-child {
            margin-top: 20px;
        }

        @include rwdCustom(1300) {
            max-width: 30px;
            max-height: 30px;
            margin-right: 25px;
            margin-top: 0px;

            &:last-child {
                margin-top: 0px;
            }
        }

        &:hover {
            transform: scale(1.05);
        }
    }

    &__menu {
        &-wrapper {
            display: none;

            @include rwdCustom(1300) {
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                justify-content: space-evenly;
                align-items: center;
            }
        }

        &-wrapper-mobile {
            display: none;
            z-index: 99;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            max-width: 100%;
            height: auto;
            background: var($--theme-background);
            color: var($--theme-text);
            flex-direction: column;
            flex-wrap: nowrap;
            justify-content: flex-start;
            align-items: center;
            padding: 0 0 32px;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.45);

            @include breakpoint-min('tablet') {
                padding: 0 0 42px;
            }

            @include breakpoint-min('large-tablet') {
                padding: 0 0 52px;
            }

            &--show {
                display: flex;

                @include rwdCustom(1300) {
                    display: none;
                }
            }
        }

        &-item {
            color: var($--theme-text);
            position: relative;
            margin-bottom: 30px;
            text-decoration: none;
            text-align: center;
            font-size: 1.2rem;
            text-transform: uppercase;
            transition: all $transitionDuration;

            @include breakpoint-min('tablet') {
                // font-size: 1.2rem;
                margin-bottom: 45px;
            }

            @include breakpoint-min('large-tablet') {
                // font-size: 1.4rem;
                margin-bottom: 60px;
            }

            @include rwdCustom(1300) {
                margin-bottom: 0;
                font-size: 16px;
                margin-right: 45px;
            }

            &:hover {
                font-weight: $semiBoldWeight;

                @include rwdCustom(1300) {
                    font-weight: $regularWeight;
                    text-decoration: underline;
                }
            }

            &:focus-visible {
                outline: 2px solid var($--theme-primary);
                outline-offset: 2px;
            }

            &:last-child {
                margin-bottom: 0;
            }

            &--active {
                border-bottom: 4px solid var($--theme-primary);
                padding: 0 0 8px 0;
                text-decoration: none;
                font-weight: $boldWeight;
                color: var($--theme-primary);

                &:hover {
                    font-weight: $boldWeight;
                    color: var($--theme-primary-pale);
                }

                @include rwdCustom(1300) {
                    border-bottom: none;
                    font-weight: $boldWeight;
                    padding: 0;

                    &::before {
                        content: '';
                        display: block;
                        width: 100%;
                        height: 3px;
                        background-color: var($--theme-primary);
                        position: absolute;
                        bottom: -10px;
                        left: 0;
                    }

                    &:hover {
                        text-decoration: none;
                    }
                }
            }

            &--separator {
                display: block;
                margin: 0 30px;
            }
        }

        &-button {
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            min-height: 44px;

            &:focus-visible {
                outline: 2px solid var($--theme-primary);
                outline-offset: 2px;
            }

            @include rwdCustom(1300) {
                display: none;
            }
        }

        &-icon {
            width: 24px;
            height: 24px;

            @include breakpoint-min('tablet') {
                width: 28px;
                height: 28px;
            }
        }
    }

    &__close-button {
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 44px;
        min-height: 44px;
        transition: transform 500ms;

        &:focus-visible {
            outline: 2px solid var($--theme-primary);
            outline-offset: 2px;
        }

        &:hover {
            transform: rotate(180deg);
        }
    }

    &__close-icon {
        width: 24px;
        height: 24px;

        @include breakpoint-min('tablet') {
            width: 28px;
            height: 28px;
        }
    }
}