.not-found {
  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 50px 0;
  }

  &__title {
    font-size: 1.8rem;
    margin-bottom: 30px;
    color: var($--theme-primary);
  }

  &__list {
    list-style: disc;
    padding: 0;
    margin: 0 0 40px 0;
    text-align: center;

    &-item {
      margin: 15px 0;
    }
  }

  &__link {
    color: var($--theme-primary);
    font-weight: $boldWeight;
    text-decoration: none;
    transition: all $transitionDuration;

    &:hover {
      text-decoration: underline;
    }
  }

  &__popular-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  &__popular-title {
    font-size: 1.4rem;
    margin-bottom: 20px;
  }

  &__buttons-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 15px;

    @include breakpoint-min('large-tablet') {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
    }
  }
}
