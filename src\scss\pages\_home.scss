.App {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    height: 100vh;
    width: 100vw;
    background-color: var($--theme-background);
    text-align: center;
}

.home {
    &__tiles-wrapper {
        width: 100%;
        max-width: 1920px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        @include breakpoint-min('large-tablet') {
            flex-direction: row;
            justify-content: center;
        }
    }

    &__number-tiles-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        padding: 25px 0;

        @include breakpoint-min('small-screen') {
            align-items: stretch;
        }
    }

    &__number-tile {
        &-wrapper {
            margin: 25px;
            background: transparentize(#b3bcad, 0.5);
            padding: 50px;
            border-radius: 12px;
            width: 75%;
            min-height: 210px;
            box-sizing: border-box;

            @include breakpoint-min('large-tablet') {
                padding: 50px 40px;
            }

            @include breakpoint-min('small-screen') {
                padding: 50px 35px;
                width: 40%;
            }

            @include breakpoint-min('desktop') {
                padding: 50px 30px;
                width: 18%;
            }
        }

        &--number {
            font-size: 3rem;
            line-height: 1.5rem;
            margin-bottom: 30px;
            color: var($--theme-primary);
            font-weight: $boldWeight;
        }

        &--text {
            font-weight: $lightWeight;
        }
    }

    &__services-overview {
        background-color: var($--theme-white-dark);
        padding: 60px 0;
        margin: 40px 0 0 0;
        width: 100%;

        @include breakpoint-min('tablet') {
            padding: 80px 0;
        }
    }

    &__services-title {
        color: var($--theme-primary);
        font-size: 1.8rem;
        font-weight: $boldWeight;
        margin-bottom: 30px;
        text-align: center;
        line-height: 2rem;

        @include breakpoint-min('tablet') {
            font-size: 2rem;
        }
    }

    &__services-content {
        max-width: 900px;
        margin: 0 auto;

        p {
            margin-bottom: 20px;
            line-height: 1.6;
            text-align: left;
            font-size: 1rem;

            @include breakpoint-min('tablet') {
                font-size: 1.1rem;
            }

            strong {
                font-weight: $semiBoldWeight;
                color: var($--theme-text);
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    &__faq-section {
        padding: 60px 0;
        margin: 0;
        width: 100%;
        background-color: var($--theme-primary-light);

        @include breakpoint-min('tablet') {
            padding: 80px 0;
        }
    }

    &__faq-title {
        color: var($--theme-primary);
        font-size: 1.8rem;
        font-weight: $boldWeight;
        margin-bottom: 40px;
        text-align: center;
        line-height: 2rem;

        @include breakpoint-min('tablet') {
            font-size: 2rem;
        }
    }

    &__faq-content {
        max-width: 900px;
        margin: 0 auto;
    }

    &__faq-item {
        margin-bottom: 30px;
        border-bottom: 1px solid var($--theme-banner-background);
        padding-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
    }

    &__faq-question {
        font-size: 1.2rem;
        font-weight: $semiBoldWeight;
        color: var($--theme-text);
        margin-bottom: 15px;
        position: relative;

        @include breakpoint-min('tablet') {
            font-size: 1.3rem;
        }
    }

    &__faq-answer {
        position: relative;

        p {
            margin: 0;
            line-height: 1.6;
            text-align: left;
            font-size: 1rem;
            text-align: justify;

            @include breakpoint-min('tablet') {
                font-size: 1.1rem;
                text-align: left;
            }
        }
    }
}

// @media (prefers-reduced-motion: no-preference) {
//     .App-logo {
//     animation: App-logo-spin infinite 20s linear;
//     }
// }

// .App-header {
//     width: 45%;
//     display: flex;
//     flex-direction: column;
//     align-items: center;
//     justify-content: center;
//     font-size: calc(10px + 2vmin);
//     color: white;
// }

// .App-link {
//     color: #61dafb;

//     &--active {
//         color: red;
//     }
// }

// @keyframes App-logo-spin {
//     from {
//     transform: rotate(0deg);
//     }
//     to {
//     transform: rotate(360deg);
//     }
// }

// .App-line {
//     height: 50%;
//     width: 2px;
//     background-color: white;
// }

// .App-main {
//     height: 100%;
//     width: 45%;
//     display: flex;
//     flex-direction: column;
//     align-items: center;
//     justify-content: center;
//     font-size: calc(10px + 2vmin);
//     color: white;
// }

// ------------

.grid-wrapper {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    max-width: $maxWidth;
    margin: 0 auto;
    padding: 30px;

    @include breakpoint-min('tablet') {
        padding: 30px 45px;
    }

    @include breakpoint-min('large-tablet') {
        padding: 30px 60px;
    }

    &__half {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: flex-start;

        @include breakpoint-min('small-screen') {
            flex-direction: row;
            justify-content: space-between;
            align-items: stretch;
        }
    }
}