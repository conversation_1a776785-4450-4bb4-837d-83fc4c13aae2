.kontakt {
    &__sections-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;

        @include breakpoint-min('small-screen') {
            flex-direction: row;
            align-items: flex-start;
            justify-content: center;
        }
    }

    &__banner {
        display: none;
        max-width: 40%;
        width: 40%;
        height: auto;
        max-height: 85vh;

        @include breakpoint-min('small-screen') {
            display: block;
        }

        @include breakpoint-min('desktop') {
            max-width: 50%;
            width: 50%;
        }

        &--mobile {
            display: block;
            width: 100%;
            height: auto;

            @include breakpoint-min('small-screen') {
                display: none;
            }
        }
    }

    &__content-wrapper {
        width: 100%;
        box-sizing: border-box;

        @include breakpoint-min('small-screen') {
            // height: 100%;
            // width: 60%;
            padding: 10px 30px;
        }

        @include breakpoint-min('desktop') {
            padding: 10px 50px;
            // width: 50%;
        }
    }

    &__section-title {
        .section-title__title {
            font-size: 1.75rem; // 28px
            line-height: 40px;
            font-weight: $lightWeight;

            @include breakpoint-min('desktop') {
                font-size: 48px;
                line-height: 60px;
            }
        }
    }

    &__contact-info {
        &-text {
            font-weight: $lightWeight;
            font-size: 1rem; // 16px
            line-height: 1.75rem; // 28px
            text-align: left;
            margin-bottom: 30px;

            @include breakpoint-min('desktop') {
                font-size: 1.2rem; // 20px
                line-height: 2rem; // 32px
            }

            &--mail {
                color: var($--theme-primary);
            }
        }

        &-group {
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
        }

        &-subtitle {
            font-weight: $regularWeight;
            // text-transform: uppercase;
            text-align: left;
            font-size: 1.2rem; // 20px
            line-height: 2rem; // 32px
        }

        &-title {
            font-weight: $lightWeight;
            // text-transform: uppercase;
            text-align: left;
            font-size: 1.2rem; // 20px
            line-height: 2rem; // 32px
        }

        &-email {
            font-weight: $lightWeight;
            text-align: left;
            font-size: 1.2rem; // 20px
            line-height: 2rem; // 32px
            color: var($--theme-primary);
        }

        &-phone {
            font-weight: $lightWeight;
            text-align: left;
            font-size: 1.2rem; // 20px
            line-height: 2rem; // 32px
        }
    }

    &__oferteo {
        &-wrapper {
            display: flex;
            flex-direction: column;
            gap: 20px;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;

            @include breakpoint-min('large-tablet') {
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                gap: 50px;
            }
        }
    }

    &__map {
        &-wrapper {
            position: relative;
            padding-bottom: 75%;
            height: 0;
            overflow: hidden;
            margin-bottom: 50px;
            background-color: transparentize(#87af69, 0.70);
            border-radius: 4px;

            @include breakpoint-min('small-screen') {
                padding-bottom: 56.25%;
            }
        }

        &-title {
            position: relative;
            z-index: 2;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            width: 100%;
            box-sizing: border-box;

            h3 {
                font-size: 1.2rem;
                margin: 0 0 5px 0;
                color: var($--theme-primary);
                font-weight: $boldWeight;
            }

            p {
                margin: 0;
                font-size: 1rem;
            }
        }

        &-accessibility {
            position: absolute;
            bottom: 20px;
            right: 55px;
            z-index: 2;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 4px;
        }

        &-link {
            color: var($--theme-primary);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: $semiBoldWeight;

            &:hover {
                text-decoration: underline;
            }

            &:focus {
                outline: none;
            }

            &:focus-visible {
                outline: 2px solid var($--theme-primary);
                outline-offset: 2px;
            }
        }

        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
    }
}