.obszar-d<PERSON><PERSON>a {
    &__back-link {
        margin-top: 25px;
        color: var($--theme-primary);
        text-align: left;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;


        &-icon {
            transform: rotate(180deg);
            margin-right: 15px;
        }
    }

    &__main-title {
        font-size: 1.8rem;
        color: var($--theme-primary);
        margin: 30px 0 20px;
        text-align: center;
        font-weight: $boldWeight;
        line-height: 2rem;

        @include breakpoint-min('small-screen') {
            font-size: 2.2rem;
            margin: 40px 0 25px;
        }
    }

    &__intro-text {
        font-size: 1rem;
        line-height: 1.6;
        text-align: left;
        margin-bottom: 30px;

        @include breakpoint-min('small-screen') {
            font-size: 1.1rem;
            line-height: 1.7;
            margin-bottom: 40px;
        }
    }

    &__lists-container {
        width: 100%;

        @include rwdCustom(960) {
            width: 50%;
        }
    }

    &__lists-wrapper {
        display: flex;
        flex-direction: column;

        @include breakpoint-min('small-screen') {
            flex-direction: row;
        }
    }

    &__locations-title {
        font-size: 1.5rem;
        color: var($--theme-text);
        margin-bottom: 20px;
        text-align: center;
        font-weight: $semiBoldWeight;

        @include breakpoint-min('small-screen') {
            font-size: 1.7rem;
            text-align: left;
        }
    }

    &__links-list {
        list-style-type: disc;
        display: flex;
        width: 100%;
        flex-direction: column;
        flex-wrap: wrap;
        padding: 0 20px;

        &:first-child {
            padding-top: 10px;

            @include breakpoint-min('small-screen') {
                padding-top: 0;
            }
        }

        &:last-of-type {
            padding-bottom: 30px;

            @include breakpoint-min('small-screen') {
                padding-bottom: 0;
            }
        }

        @include rwdCustom(960) {
            padding: 0 20px;
        }

        &-item {
            width: fit-content;
            text-align: left;
            margin: 8px 0;
        }
    }

    &__link {
        color: var($--theme-primary);
        font-weight: $boldWeight;
        transition: all 0.2s ease;

        &:hover {
            text-decoration: underline;
        }
    }

    &__services-overview {
        margin: 40px 0;
        padding: 30px;
        background-color: rgba(179, 188, 173, 0.1);
        border-radius: 8px;

        @include breakpoint-min('small-screen') {
            padding: 40px;
            margin: 60px 0;
        }
    }

    &__services-overview-title {
        font-size: 1.5rem;
        color: var($--theme-primary);
        margin-bottom: 20px;
        text-align: center;
        font-weight: $boldWeight;
        line-height: 2rem;

        @include breakpoint-min('small-screen') {
            font-size: 1.8rem;
        }
    }

    &__services-overview-text {
        font-size: 1rem;
        line-height: 1.6;
        text-align: left;
        margin-bottom: 20px;

        @include breakpoint-min('small-screen') {
            font-size: 1.1rem;
            line-height: 1.7;
        }
    }

    &__services-overview-list {
        list-style-type: disc;
        padding-left: 20px;
        margin: 20px 0 30px;

        li {
            margin: 10px 0;
            text-align: left;
            font-size: 1rem;
            line-height: 1.5;

            @include breakpoint-min('small-screen') {
                font-size: 1.1rem;
            }
        }
    }

    &__city-title {
        font-size: 1.8rem;
        color: var($--theme-primary);
        margin: 30px 0 10px;
        text-align: left;
        font-weight: $boldWeight;
        line-height: 2rem;

        @include breakpoint-min('small-screen') {
            font-size: 2.2rem;
        }
    }

    &__services-section {
        margin: 40px 0;
        padding: 30px;
        background-color: rgba(179, 188, 173, 0.1);
        border-radius: 8px;

        @include breakpoint-min('small-screen') {
            padding: 40px;
        }
    }

    &__services-title {
        font-size: 1.4rem;
        color: var($--theme-text);
        margin-bottom: 20px;
        text-align: left;
        font-weight: $semiBoldWeight;
        line-height: 1.8rem;

        @include breakpoint-min('small-screen') {
            font-size: 1.6rem;
        }
    }

    &__services-list {
        list-style-type: disc;
        padding-left: 20px;
        margin-bottom: 30px;

        li {
            margin: 10px 0;
            text-align: left;
            font-size: 1rem;
            line-height: 1.5;

            @include breakpoint-min('small-screen') {
                font-size: 1.1rem;
            }
        }
    }

    &__cta {
        display: flex;
        justify-content: center;
        margin-top: 30px;

        .custom-button {
            padding: 12px 20px;
            font-size: 1rem;
            background-color: var($--theme-primary);
            color: white;
            border-radius: 4px;

            &__icon {
                filter: brightness(0) invert(1);
            }

            &:hover {
                background-color: darken(#4D6A4D, 10%);
            }

            @include breakpoint-min('small-screen') {
                padding: 15px 25px;
                font-size: 1.1rem;
            }
        }
    }
}