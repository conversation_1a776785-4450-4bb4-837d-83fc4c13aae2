/**
 * Scrolls the window to the top of the page
 * @param smooth Whether to use smooth scrolling (default: true)
 */
export const scrollToTop = (smooth: boolean = true): void => {
  window.scrollTo({
    top: 0,
    behavior: smooth ? 'smooth' : 'auto'
  });
};

/**
 * Scrolls the window to a specific element
 * @param elementId The ID of the element to scroll to
 * @param smooth Whether to use smooth scrolling (default: true)
 * @param offset Offset in pixels from the top of the element (default: 0)
 */
export const scrollToElement = (
  elementId: string,
  smooth: boolean = true,
  offset: number = 0
): void => {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: smooth ? 'smooth' : 'auto'
    });
  }
};
