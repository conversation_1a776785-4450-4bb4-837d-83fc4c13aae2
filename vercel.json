{"headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://maps-api-ssl.google.com https://www.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https://maps-api-ssl.google.com https://www.oferteo.pl https://www.google.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self'; frame-src https://maps-api-ssl.google.com https://www.google.com; object-src 'none'; base-uri 'self'; form-action 'self';"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(self), interest-cohort=()"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}